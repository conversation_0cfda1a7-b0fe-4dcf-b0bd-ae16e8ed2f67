2025-07-30 17:43:37 - business - INFO - BUSINESS_OP: {"operation": "test_operation", "timestamp": "2025-07-30T17:43:37.180503", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "user_agent": "python-requests/2.31.0", "details": {"test_field": "test_value", "timestamp": 1753868616.1593199}}
2025-07-30 17:43:38 - business - ERROR - ERROR_OP: {"operation": "test_error_operation", "error": "This is a test error", "timestamp": "2025-07-30T17:43:38.187680", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "user_agent": "python-requests/2.31.0", "details": {"error": "This is a test error", "stack": "Test stack trace"}}
2025-07-30 17:43:39 - business - INFO - USER_ACTION: {"action": "test_user_action", "timestamp": "2025-07-30T17:43:39.193860", "user_id": "test_user", "session_id": "test_session", "ip_address": "127.0.0.1", "details": {"action_type": "click", "element": "test_button"}}
{"timestamp": "2025-07-31T11:14:07.305591", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.306091", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"ef2319f2-9a92-4c80-91b5-3877e6c56c9f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "ef2319f2-9a92-4c80-91b5-3877e6c56c9f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.320592", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.016s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.321092", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.015501, \"request_id\": \"ef2319f2-9a92-4c80-91b5-3877e6c56c9f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.015501, "request_id": "ef2319f2-9a92-4c80-91b5-3877e6c56c9f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.353098", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.353600", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"1108aef3-0ca6-4459-900a-a7e2141dbde5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1108aef3-0ca6-4459-900a-a7e2141dbde5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.355099", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.355599", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002501, \"request_id\": \"1108aef3-0ca6-4459-900a-a7e2141dbde5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002501, "request_id": "1108aef3-0ca6-4459-900a-a7e2141dbde5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.367601", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.368601", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.368601", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.369601", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e43342c7-5f46-46c9-8fdf-c24f274b9ed6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e43342c7-5f46-46c9-8fdf-c24f274b9ed6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.370602", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7c1e0da0-5d29-4882-8aac-95ba89f9ba6f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7c1e0da0-5d29-4882-8aac-95ba89f9ba6f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.370602", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.371101", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"88436893-edf4-4c73-b3ff-eb22a44c4c14\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "88436893-edf4-4c73-b3ff-eb22a44c4c14"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.371101", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.371601", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c1158b96-d9c9-49ea-8d34-4ba03d05b202\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c1158b96-d9c9-49ea-8d34-4ba03d05b202"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.372102", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.372602", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.372602", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5e4d41e1-57c4-40d2-adc5-2b18facd2557\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5e4d41e1-57c4-40d2-adc5-2b18facd2557"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.373102", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005, \"request_id\": \"e43342c7-5f46-46c9-8fdf-c24f274b9ed6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005, "request_id": "e43342c7-5f46-46c9-8fdf-c24f274b9ed6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.373602", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.373602", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005002, \"request_id\": \"7c1e0da0-5d29-4882-8aac-95ba89f9ba6f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005002, "request_id": "7c1e0da0-5d29-4882-8aac-95ba89f9ba6f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.374603", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.375102", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.375602", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005001, \"request_id\": \"88436893-edf4-4c73-b3ff-eb22a44c4c14\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005001, "request_id": "88436893-edf4-4c73-b3ff-eb22a44c4c14"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.376103", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005, \"request_id\": \"c1158b96-d9c9-49ea-8d34-4ba03d05b202\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005, "request_id": "c1158b96-d9c9-49ea-8d34-4ba03d05b202"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.376103", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.004501, \"request_id\": \"5e4d41e1-57c4-40d2-adc5-2b18facd2557\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.004501, "request_id": "5e4d41e1-57c4-40d2-adc5-2b18facd2557"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.473619", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.474119", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"1098a813-57e2-492b-b5af-30b68741615b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1098a813-57e2-492b-b5af-30b68741615b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.475620", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.475620", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002001, \"request_id\": \"1098a813-57e2-492b-b5af-30b68741615b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002001, "request_id": "1098a813-57e2-492b-b5af-30b68741615b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.483621", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.484121", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"51a1d6a0-03ad-444d-8741-2f4ed0b6acca\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "51a1d6a0-03ad-444d-8741-2f4ed0b6acca"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:07.485621", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:07.485621", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.0015, \"request_id\": \"51a1d6a0-03ad-444d-8741-2f4ed0b6acca\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.0015, "request_id": "51a1d6a0-03ad-444d-8741-2f4ed0b6acca"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.439788", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.439788", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"beeb407d-8fcd-472c-a732-cd424eb15c0b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "beeb407d-8fcd-472c-a732-cd424eb15c0b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.440789", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.441289", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0015, \"request_id\": \"beeb407d-8fcd-472c-a732-cd424eb15c0b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0015, "request_id": "beeb407d-8fcd-472c-a732-cd424eb15c0b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.471795", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.472795", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f495ed49-9bb3-437b-9975-79817a0e47ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f495ed49-9bb3-437b-9975-79817a0e47ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.476296", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.476795", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.477296", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.477795", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.477795", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.478296", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c86525a6-0899-433a-bb88-1715d8798a64\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c86525a6-0899-433a-bb88-1715d8798a64"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.478296", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.478796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e9238cc9-4a9b-4ae4-898a-701ff6cf0928\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e9238cc9-4a9b-4ae4-898a-701ff6cf0928"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.478796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e53b90ce-d93b-490e-a1a0-a8493c802f9b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e53b90ce-d93b-490e-a1a0-a8493c802f9b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.479295", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"dd234632-5adc-4999-a5cb-406ae970ba2b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "dd234632-5adc-4999-a5cb-406ae970ba2b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.479295", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005001, \"request_id\": \"f495ed49-9bb3-437b-9975-79817a0e47ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005001, "request_id": "f495ed49-9bb3-437b-9975-79817a0e47ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.479796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"54d9e3cb-0f27-4b9c-9a06-34369359dec3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "54d9e3cb-0f27-4b9c-9a06-34369359dec3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.480296", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.481296", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.481796", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.482296", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.482796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006501, \"request_id\": \"c86525a6-0899-433a-bb88-1715d8798a64\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006501, "request_id": "c86525a6-0899-433a-bb88-1715d8798a64"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.482796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006002, \"request_id\": \"e9238cc9-4a9b-4ae4-898a-701ff6cf0928\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006002, "request_id": "e9238cc9-4a9b-4ae4-898a-701ff6cf0928"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.482796", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006001, \"request_id\": \"e53b90ce-d93b-490e-a1a0-a8493c802f9b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006001, "request_id": "e53b90ce-d93b-490e-a1a0-a8493c802f9b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.483296", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.483797", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006001, \"request_id\": \"dd234632-5adc-4999-a5cb-406ae970ba2b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006001, "request_id": "dd234632-5adc-4999-a5cb-406ae970ba2b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.485296", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006, \"request_id\": \"54d9e3cb-0f27-4b9c-9a06-34369359dec3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006, "request_id": "54d9e3cb-0f27-4b9c-9a06-34369359dec3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.552809", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.553309", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"fe4ef1af-43d2-45dd-ab95-c492aef2268a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "fe4ef1af-43d2-45dd-ab95-c492aef2268a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.555309", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.555809", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.0025, \"request_id\": \"fe4ef1af-43d2-45dd-ab95-c492aef2268a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.0025, "request_id": "fe4ef1af-43d2-45dd-ab95-c492aef2268a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.568311", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.568811", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a4ea9a38-ab7a-4447-b4e1-6313f749d6ba\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a4ea9a38-ab7a-4447-b4e1-6313f749d6ba"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:08.570312", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:08.570812", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002, \"request_id\": \"a4ea9a38-ab7a-4447-b4e1-6313f749d6ba\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002, "request_id": "a4ea9a38-ab7a-4447-b4e1-6313f749d6ba"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.233928", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.234428", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5819e5bc-6e8e-4176-ba21-a8fe5030cff9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5819e5bc-6e8e-4176-ba21-a8fe5030cff9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.235929", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.235929", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"5819e5bc-6e8e-4176-ba21-a8fe5030cff9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "5819e5bc-6e8e-4176-ba21-a8fe5030cff9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.262433", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.263934", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.264434", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.265933", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"1c5ea49e-cc90-4191-a978-22c37fb152da\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1c5ea49e-cc90-4191-a978-22c37fb152da"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.266934", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c402123c-5af9-496d-82be-333503a358d4\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c402123c-5af9-496d-82be-333503a358d4"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.267435", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.267935", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"253ddb47-acdc-4e49-ab53-d2d7670ebbae\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "253ddb47-acdc-4e49-ab53-d2d7670ebbae"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.268435", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.268934", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"883c2fbd-ce31-4900-87fe-c20efb41fc1f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "883c2fbd-ce31-4900-87fe-c20efb41fc1f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.269935", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.269935", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.270435", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.270936", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"d2e34732-e504-4dd8-a167-06e173a00465\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "d2e34732-e504-4dd8-a167-06e173a00465"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.271435", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.271435", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c7bbf61b-b59f-4710-b257-6aceaf99ab24\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c7bbf61b-b59f-4710-b257-6aceaf99ab24"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.271935", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.271935", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.008001, \"request_id\": \"1c5ea49e-cc90-4191-a978-22c37fb152da\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.008001, "request_id": "1c5ea49e-cc90-4191-a978-22c37fb152da"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.272435", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.007502, \"request_id\": \"c402123c-5af9-496d-82be-333503a358d4\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.007502, "request_id": "c402123c-5af9-496d-82be-333503a358d4"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.272435", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.007001, \"request_id\": \"253ddb47-acdc-4e49-ab53-d2d7670ebbae\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.007001, "request_id": "253ddb47-acdc-4e49-ab53-d2d7670ebbae"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.272935", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006001, \"request_id\": \"883c2fbd-ce31-4900-87fe-c20efb41fc1f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006001, "request_id": "883c2fbd-ce31-4900-87fe-c20efb41fc1f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.273435", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.273935", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.275936", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.007, \"request_id\": \"d2e34732-e504-4dd8-a167-06e173a00465\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.007, "request_id": "d2e34732-e504-4dd8-a167-06e173a00465"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.276936", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.005, \"request_id\": \"c7bbf61b-b59f-4710-b257-6aceaf99ab24\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.005, "request_id": "c7bbf61b-b59f-4710-b257-6aceaf99ab24"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.345447", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.345948", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"61bfbf36-eaf6-43b7-8261-7aeace640a9a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "61bfbf36-eaf6-43b7-8261-7aeace640a9a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.347948", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.347948", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.0025, \"request_id\": \"61bfbf36-eaf6-43b7-8261-7aeace640a9a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.0025, "request_id": "61bfbf36-eaf6-43b7-8261-7aeace640a9a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.362451", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.362951", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f11ba774-7354-4395-9a03-ea8686daaede\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f11ba774-7354-4395-9a03-ea8686daaede"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T11:14:09.364451", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T11:14:09.364951", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002, \"request_id\": \"f11ba774-7354-4395-9a03-ea8686daaede\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002, "request_id": "f11ba774-7354-4395-9a03-ea8686daaede"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.300530", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.302530", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"bc50be12-6988-421b-befc-40b8edefe7c5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "bc50be12-6988-421b-befc-40b8edefe7c5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.319033", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.020s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.319533", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.019503, \"request_id\": \"bc50be12-6988-421b-befc-40b8edefe7c5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.019503, "request_id": "bc50be12-6988-421b-befc-40b8edefe7c5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.355039", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.355539", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"6ed9e915-ed79-44de-97ee-71e048e77d2f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "6ed9e915-ed79-44de-97ee-71e048e77d2f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.358039", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.358039", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.003, \"request_id\": \"6ed9e915-ed79-44de-97ee-71e048e77d2f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.003, "request_id": "6ed9e915-ed79-44de-97ee-71e048e77d2f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.371041", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.371542", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.372542", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.372542", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.373042", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9702d6c0-fbb2-4678-89ad-5d4389faf6dc\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9702d6c0-fbb2-4678-89ad-5d4389faf6dc"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.373042", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.373543", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"ca441619-9e80-4296-b2a9-502690583230\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "ca441619-9e80-4296-b2a9-502690583230"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.373543", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a3b5cf80-5427-4dbf-b72b-aa56533a8160\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a3b5cf80-5427-4dbf-b72b-aa56533a8160"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.374043", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f74834da-962a-473b-863a-0030c91545ca\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f74834da-962a-473b-863a-0030c91545ca"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.374542", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5015c342-d8b9-4051-b718-db51c7d0cd37\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5015c342-d8b9-4051-b718-db51c7d0cd37"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.375043", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.375543", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.376042", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.376543", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006001, \"request_id\": \"9702d6c0-fbb2-4678-89ad-5d4389faf6dc\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006001, "request_id": "9702d6c0-fbb2-4678-89ad-5d4389faf6dc"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.377042", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005501, \"request_id\": \"ca441619-9e80-4296-b2a9-502690583230\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005501, "request_id": "ca441619-9e80-4296-b2a9-502690583230"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.377543", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.377543", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005502, \"request_id\": \"a3b5cf80-5427-4dbf-b72b-aa56533a8160\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005502, "request_id": "a3b5cf80-5427-4dbf-b72b-aa56533a8160"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.378043", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.378543", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005001, \"request_id\": \"f74834da-962a-473b-863a-0030c91545ca\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005001, "request_id": "f74834da-962a-473b-863a-0030c91545ca"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.380044", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.004501, \"request_id\": \"5015c342-d8b9-4051-b718-db51c7d0cd37\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.004501, "request_id": "5015c342-d8b9-4051-b718-db51c7d0cd37"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.492063", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.492563", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"2c22ef98-69b7-4690-8336-8f86fe88ad93\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "2c22ef98-69b7-4690-8336-8f86fe88ad93"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.494563", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.495064", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.0025, \"request_id\": \"2c22ef98-69b7-4690-8336-8f86fe88ad93\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.0025, "request_id": "2c22ef98-69b7-4690-8336-8f86fe88ad93"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.503565", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.504065", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"3b6f15d2-15f1-4a5e-be7c-6a70aa7cfb98\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "3b6f15d2-15f1-4a5e-be7c-6a70aa7cfb98"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:25.505566", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:25.506066", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002001, \"request_id\": \"3b6f15d2-15f1-4a5e-be7c-6a70aa7cfb98\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002001, "request_id": "3b6f15d2-15f1-4a5e-be7c-6a70aa7cfb98"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.585430", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.585931", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"73f4ffb3-3dbf-4d43-a221-bdc57389d507\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "73f4ffb3-3dbf-4d43-a221-bdc57389d507"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.587430", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.587930", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"73f4ffb3-3dbf-4d43-a221-bdc57389d507\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "73f4ffb3-3dbf-4d43-a221-bdc57389d507"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.619436", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.619936", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"0c800ec6-5906-4e24-ad01-d1aac7e17b0f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "0c800ec6-5906-4e24-ad01-d1aac7e17b0f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.620937", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.621437", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002001, \"request_id\": \"0c800ec6-5906-4e24-ad01-d1aac7e17b0f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002001, "request_id": "0c800ec6-5906-4e24-ad01-d1aac7e17b0f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.630940", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.631938", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.632939", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"bd1c17c3-2878-4136-9e0f-2b4b9dab5c10\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "bd1c17c3-2878-4136-9e0f-2b4b9dab5c10"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.633439", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c0cd83bb-3dd8-4df7-bb88-a8320e3a013d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c0cd83bb-3dd8-4df7-bb88-a8320e3a013d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.633938", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.634939", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.635438", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.635939", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"327c5ca9-7ecb-4db8-95ac-3b9b3adb0d41\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "327c5ca9-7ecb-4db8-95ac-3b9b3adb0d41"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.636438", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.636438", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"8068bdd8-a51c-48e1-915d-18f03dbef151\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "8068bdd8-a51c-48e1-915d-18f03dbef151"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.636939", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7ce1f08b-8acb-42da-b04b-0201f4530348\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7ce1f08b-8acb-42da-b04b-0201f4530348"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.636939", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.637439", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.004999, \"request_id\": \"bd1c17c3-2878-4136-9e0f-2b4b9dab5c10\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.004999, "request_id": "bd1c17c3-2878-4136-9e0f-2b4b9dab5c10"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.637939", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.637939", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.638439", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.004501, \"request_id\": \"c0cd83bb-3dd8-4df7-bb88-a8320e3a013d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.004501, "request_id": "c0cd83bb-3dd8-4df7-bb88-a8320e3a013d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.638939", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.639440", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0045, \"request_id\": \"327c5ca9-7ecb-4db8-95ac-3b9b3adb0d41\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0045, "request_id": "327c5ca9-7ecb-4db8-95ac-3b9b3adb0d41"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.639440", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0045, \"request_id\": \"8068bdd8-a51c-48e1-915d-18f03dbef151\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0045, "request_id": "8068bdd8-a51c-48e1-915d-18f03dbef151"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.640439", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0035, \"request_id\": \"7ce1f08b-8acb-42da-b04b-0201f4530348\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0035, "request_id": "7ce1f08b-8acb-42da-b04b-0201f4530348"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.766462", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.766962", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"aeee5895-a601-4b2b-aa64-ff448f29b7ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "aeee5895-a601-4b2b-aa64-ff448f29b7ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.768462", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.768962", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"aeee5895-a601-4b2b-aa64-ff448f29b7ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "aeee5895-a601-4b2b-aa64-ff448f29b7ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.777465", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.777965", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"b75c11cd-ac76-4319-ac5a-2e91217223d3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "b75c11cd-ac76-4319-ac5a-2e91217223d3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:27.779464", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:27.779964", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002499, \"request_id\": \"b75c11cd-ac76-4319-ac5a-2e91217223d3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002499, "request_id": "b75c11cd-ac76-4319-ac5a-2e91217223d3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.000354", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.000354", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"3a0ecff7-5782-4cf5-baab-0ac6c6d2684a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "3a0ecff7-5782-4cf5-baab-0ac6c6d2684a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.001854", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.001854", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"3a0ecff7-5782-4cf5-baab-0ac6c6d2684a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "3a0ecff7-5782-4cf5-baab-0ac6c6d2684a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.029359", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.029859", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"14cc3f7f-4f56-4db0-ad07-1d1642182f09\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "14cc3f7f-4f56-4db0-ad07-1d1642182f09"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.030859", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.031359", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"14cc3f7f-4f56-4db0-ad07-1d1642182f09\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "14cc3f7f-4f56-4db0-ad07-1d1642182f09"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.049362", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.049863", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.049863", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.049863", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.050363", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.050363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"daddce69-4823-4bd8-a340-52bdf72bcbd1\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "daddce69-4823-4bd8-a340-52bdf72bcbd1"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.050863", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"1ad5d2ea-b08a-474b-a7ce-ce0b40d071ab\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1ad5d2ea-b08a-474b-a7ce-ce0b40d071ab"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.051363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"174e1e0f-1e60-4adc-8071-7874f8c0f3d3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "174e1e0f-1e60-4adc-8071-7874f8c0f3d3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.051363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"0033b711-752c-4425-84bf-0900a16fc15f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "0033b711-752c-4425-84bf-0900a16fc15f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.051862", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"bad628f7-aab2-4808-bc0d-db7f4252b56e\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "bad628f7-aab2-4808-bc0d-db7f4252b56e"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.052863", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.052863", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.053863", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005, \"request_id\": \"daddce69-4823-4bd8-a340-52bdf72bcbd1\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005, "request_id": "daddce69-4823-4bd8-a340-52bdf72bcbd1"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.053863", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.054364", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.054864", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005001, \"request_id\": \"1ad5d2ea-b08a-474b-a7ce-ce0b40d071ab\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005001, "request_id": "1ad5d2ea-b08a-474b-a7ce-ce0b40d071ab"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.054864", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.055363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005501, \"request_id\": \"174e1e0f-1e60-4adc-8071-7874f8c0f3d3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005501, "request_id": "174e1e0f-1e60-4adc-8071-7874f8c0f3d3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.055363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005, \"request_id\": \"0033b711-752c-4425-84bf-0900a16fc15f\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005, "request_id": "0033b711-752c-4425-84bf-0900a16fc15f"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.055864", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005, \"request_id\": \"bad628f7-aab2-4808-bc0d-db7f4252b56e\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005, "request_id": "bad628f7-aab2-4808-bc0d-db7f4252b56e"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.171383", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.171383", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"0bb6fbae-2c1f-40a9-baad-d663a1ff5434\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "0bb6fbae-2c1f-40a9-baad-d663a1ff5434"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.172885", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.173384", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002001, \"request_id\": \"0bb6fbae-2c1f-40a9-baad-d663a1ff5434\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002001, "request_id": "0bb6fbae-2c1f-40a9-baad-d663a1ff5434"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.189386", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.189386", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"04a681fc-8c68-436c-9306-a30227046000\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "04a681fc-8c68-436c-9306-a30227046000"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:30.191387", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:30.191887", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.002, \"request_id\": \"04a681fc-8c68-436c-9306-a30227046000\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.002, "request_id": "04a681fc-8c68-436c-9306-a30227046000"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:31.568143", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:31.568143", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:31.568643", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"2fe73240-3360-4c73-995e-3c48a5ae48dc\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "2fe73240-3360-4c73-995e-3c48a5ae48dc"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:31.569144", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e31940a0-ef2f-42aa-a5a0-ff8747c28d58\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e31940a0-ef2f-42aa-a5a0-ff8747c28d58"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:31.570644", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:31.570644", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:31.571144", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.003001, \"request_id\": \"2fe73240-3360-4c73-995e-3c48a5ae48dc\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.003001, "request_id": "2fe73240-3360-4c73-995e-3c48a5ae48dc"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:31.571144", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.003, \"request_id\": \"e31940a0-ef2f-42aa-a5a0-ff8747c28d58\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.003, "request_id": "e31940a0-ef2f-42aa-a5a0-ff8747c28d58"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.706093", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.707094", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"0824a548-2c15-448d-9ca7-6fac0a4b1371\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "0824a548-2c15-448d-9ca7-6fac0a4b1371"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.708093", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.708593", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.003001, \"request_id\": \"0824a548-2c15-448d-9ca7-6fac0a4b1371\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.003001, "request_id": "0824a548-2c15-448d-9ca7-6fac0a4b1371"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.761602", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.762602", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"6869175e-678d-4578-9768-1135ea170170\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "6869175e-678d-4578-9768-1135ea170170"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.766103", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.766603", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.767603", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"31c838bb-9a89-4332-8ee3-ef68aa3aa704\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "31c838bb-9a89-4332-8ee3-ef68aa3aa704"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.770104", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.771604", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"bd2221ef-1481-40e0-8855-83e230a2f3e3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "bd2221ef-1481-40e0-8855-83e230a2f3e3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.773104", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005501, \"request_id\": \"6869175e-678d-4578-9768-1135ea170170\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005501, "request_id": "6869175e-678d-4578-9768-1135ea170170"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.773604", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.777105", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.779605", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.012s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.780606", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.782106", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.015s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.782606", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e67c9562-27e1-41da-8a6a-6ddde78e4112\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e67c9562-27e1-41da-8a6a-6ddde78e4112"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.783106", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.011503, \"request_id\": \"31c838bb-9a89-4332-8ee3-ef68aa3aa704\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.011503, "request_id": "31c838bb-9a89-4332-8ee3-ef68aa3aa704"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.783606", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"4b448258-189b-4cc1-80f9-796998b13681\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "4b448258-189b-4cc1-80f9-796998b13681"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.783606", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"49f68fa6-ee39-4267-a937-da35f5f5805c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "49f68fa6-ee39-4267-a937-da35f5f5805c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.784106", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.015003, \"request_id\": \"bd2221ef-1481-40e0-8855-83e230a2f3e3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.015003, "request_id": "bd2221ef-1481-40e0-8855-83e230a2f3e3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.787107", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.014s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.791107", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.022s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.797608", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.018s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.801609", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.013502, \"request_id\": \"e67c9562-27e1-41da-8a6a-6ddde78e4112\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.013502, "request_id": "e67c9562-27e1-41da-8a6a-6ddde78e4112"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.802109", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.021504, \"request_id\": \"4b448258-189b-4cc1-80f9-796998b13681\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.021504, "request_id": "4b448258-189b-4cc1-80f9-796998b13681"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.802609", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.018003, \"request_id\": \"49f68fa6-ee39-4267-a937-da35f5f5805c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.018003, "request_id": "49f68fa6-ee39-4267-a937-da35f5f5805c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.995644", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.996144", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"747bb5a2-c51a-4199-898f-5b0ea10958da\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "747bb5a2-c51a-4199-898f-5b0ea10958da"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:39.998644", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:39.999144", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.003, \"request_id\": \"747bb5a2-c51a-4199-898f-5b0ea10958da\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.003, "request_id": "747bb5a2-c51a-4199-898f-5b0ea10958da"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:40.013646", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:40.014146", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"56418865-adde-42dc-a7db-7d42b4c9968a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "56418865-adde-42dc-a7db-7d42b4c9968a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:25:40.015646", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:25:40.016146", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"56418865-adde-42dc-a7db-7d42b4c9968a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "56418865-adde-42dc-a7db-7d42b4c9968a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.454573", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.455073", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9b625cc0-3682-407b-87e6-6f74ed67abf7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9b625cc0-3682-407b-87e6-6f74ed67abf7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.469075", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.016s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.469576", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.016002, \"request_id\": \"9b625cc0-3682-407b-87e6-6f74ed67abf7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.016002, "request_id": "9b625cc0-3682-407b-87e6-6f74ed67abf7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.513085", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.513085", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5d53b6e5-164f-4687-9780-6659fe6f1a34\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5d53b6e5-164f-4687-9780-6659fe6f1a34"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.515084", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.515583", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.003001, \"request_id\": \"5d53b6e5-164f-4687-9780-6659fe6f1a34\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.003001, "request_id": "5d53b6e5-164f-4687-9780-6659fe6f1a34"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.536588", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.536588", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.538087", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.538587", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7b3b96fb-c587-48af-8133-e21f862a0971\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7b3b96fb-c587-48af-8133-e21f862a0971"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.539588", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"42d32e0b-0613-4eff-92a2-ea156283c4bb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "42d32e0b-0613-4eff-92a2-ea156283c4bb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.541089", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"4cc08911-d264-4390-805e-57ebe5a0c087\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "4cc08911-d264-4390-805e-57ebe5a0c087"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.541089", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.542088", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.542588", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.543088", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9e40c585-05e6-4e23-9903-bdf2aead1bce\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9e40c585-05e6-4e23-9903-bdf2aead1bce"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.543589", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.544088", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5aacbe37-9e74-4139-809d-00f8f9d96c08\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5aacbe37-9e74-4139-809d-00f8f9d96c08"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.544088", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.544589", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.007501, \"request_id\": \"7b3b96fb-c587-48af-8133-e21f862a0971\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.007501, "request_id": "7b3b96fb-c587-48af-8133-e21f862a0971"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.545590", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.548589", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.007001, \"request_id\": \"42d32e0b-0613-4eff-92a2-ea156283c4bb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.007001, "request_id": "42d32e0b-0613-4eff-92a2-ea156283c4bb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.549090", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006502, \"request_id\": \"4cc08911-d264-4390-805e-57ebe5a0c087\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006502, "request_id": "4cc08911-d264-4390-805e-57ebe5a0c087"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.550089", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:13.550590", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.006001, \"request_id\": \"9e40c585-05e6-4e23-9903-bdf2aead1bce\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.006001, "request_id": "9e40c585-05e6-4e23-9903-bdf2aead1bce"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:13.552590", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.009003, \"request_id\": \"5aacbe37-9e74-4139-809d-00f8f9d96c08\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.009003, "request_id": "5aacbe37-9e74-4139-809d-00f8f9d96c08"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:16.191555", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:16.192054", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:16.192555", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"46ced7d3-7247-47bb-ac23-5f127303eee1\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "46ced7d3-7247-47bb-ac23-5f127303eee1"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:16.192555", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a31c66dc-7ed4-49a6-bcc9-28ed63bd6084\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a31c66dc-7ed4-49a6-bcc9-28ed63bd6084"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:16.194554", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:16.195054", "level": "INFO", "logger": "app", "message": "响应完成: 304 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:26:16.195554", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.004002, \"request_id\": \"46ced7d3-7247-47bb-ac23-5f127303eee1\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.004002, "request_id": "46ced7d3-7247-47bb-ac23-5f127303eee1"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:26:16.196054", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 304, \"duration\": 0.003501, \"request_id\": \"a31c66dc-7ed4-49a6-bcc9-28ed63bd6084\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 304, "duration": 0.003501, "request_id": "a31c66dc-7ed4-49a6-bcc9-28ed63bd6084"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.561638", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.562138", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f6013522-375e-4a7e-91e7-fd710b650c50\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f6013522-375e-4a7e-91e7-fd710b650c50"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.564139", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.564139", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002501, \"request_id\": \"f6013522-375e-4a7e-91e7-fd710b650c50\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002501, "request_id": "f6013522-375e-4a7e-91e7-fd710b650c50"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.599644", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.601145", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"22628a53-39e3-4945-be53-390691ee0522\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "22628a53-39e3-4945-be53-390691ee0522"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.602146", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.604145", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.606147", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.607146", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e2050b4e-0573-46d4-8153-9f3e439c877e\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e2050b4e-0573-46d4-8153-9f3e439c877e"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.607646", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.608646", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"c9f8d027-c657-464e-a31d-89e5df63b89a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "c9f8d027-c657-464e-a31d-89e5df63b89a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.609646", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"1269ca2b-734f-492b-b30d-5ceb72a5b9b2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "1269ca2b-734f-492b-b30d-5ceb72a5b9b2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.610146", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.611146", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007, \"request_id\": \"22628a53-39e3-4945-be53-390691ee0522\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007, "request_id": "22628a53-39e3-4945-be53-390691ee0522"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.611648", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.612147", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.010s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.612647", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a0bec561-9025-4495-9735-6cda0008d510\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a0bec561-9025-4495-9735-6cda0008d510"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.612647", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.613148", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.613647", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7565a096-e88a-4fc9-a658-580105016c60\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7565a096-e88a-4fc9-a658-580105016c60"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.614147", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.010001, \"request_id\": \"e2050b4e-0573-46d4-8153-9f3e439c877e\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.010001, "request_id": "e2050b4e-0573-46d4-8153-9f3e439c877e"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.614647", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008001, \"request_id\": \"c9f8d027-c657-464e-a31d-89e5df63b89a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008001, "request_id": "c9f8d027-c657-464e-a31d-89e5df63b89a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.614647", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007002, \"request_id\": \"1269ca2b-734f-492b-b30d-5ceb72a5b9b2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007002, "request_id": "1269ca2b-734f-492b-b30d-5ceb72a5b9b2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.615647", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.616648", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.617648", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008001, \"request_id\": \"a0bec561-9025-4495-9735-6cda0008d510\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008001, "request_id": "a0bec561-9025-4495-9735-6cda0008d510"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.619648", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007, \"request_id\": \"7565a096-e88a-4fc9-a658-580105016c60\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007, "request_id": "7565a096-e88a-4fc9-a658-580105016c60"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.813182", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.814183", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"6c2056e7-12bd-4339-9e4a-efbbbfe29787\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "6c2056e7-12bd-4339-9e4a-efbbbfe29787"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.816682", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.817182", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.003501, \"request_id\": \"6c2056e7-12bd-4339-9e4a-efbbbfe29787\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.003501, "request_id": "6c2056e7-12bd-4339-9e4a-efbbbfe29787"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.831186", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.832185", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5d3370f9-55aa-4c72-a4a7-f3928379c7a3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5d3370f9-55aa-4c72-a4a7-f3928379c7a3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:35:51.834686", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:35:51.835186", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0035, \"request_id\": \"5d3370f9-55aa-4c72-a4a7-f3928379c7a3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0035, "request_id": "5d3370f9-55aa-4c72-a4a7-f3928379c7a3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.029407", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.029906", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"65bde4af-09db-43b9-aa73-ce3a8051408d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "65bde4af-09db-43b9-aa73-ce3a8051408d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.030906", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.031407", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"65bde4af-09db-43b9-aa73-ce3a8051408d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "65bde4af-09db-43b9-aa73-ce3a8051408d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.064912", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.065412", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.066914", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.066914", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.067412", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.067914", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7bc000a1-240d-4388-a0a4-96821b55aa39\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7bc000a1-240d-4388-a0a4-96821b55aa39"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.067914", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.068413", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"abb2273c-51dc-45bb-a7c2-9e8543c50c8b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "abb2273c-51dc-45bb-a7c2-9e8543c50c8b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.068413", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"3e50033b-21be-4610-9ded-8efb5b8c03e0\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "3e50033b-21be-4610-9ded-8efb5b8c03e0"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.068914", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9a737403-a2b9-40a1-b7f4-abd4258f9887\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9a737403-a2b9-40a1-b7f4-abd4258f9887"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.069414", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"cfede2d5-df6c-4a57-8340-2cae3deb13c2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "cfede2d5-df6c-4a57-8340-2cae3deb13c2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.070414", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"184b31db-f5ee-4ca4-982f-4d1801232451\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "184b31db-f5ee-4ca4-982f-4d1801232451"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.070914", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.072414", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.010s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.072913", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.073914", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008501, \"request_id\": \"7bc000a1-240d-4388-a0a4-96821b55aa39\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008501, "request_id": "7bc000a1-240d-4388-a0a4-96821b55aa39"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.074415", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.010s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.074415", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.009501, \"request_id\": \"abb2273c-51dc-45bb-a7c2-9e8543c50c8b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.009501, "request_id": "abb2273c-51dc-45bb-a7c2-9e8543c50c8b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.074914", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.010s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.074914", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.075414", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008502, \"request_id\": \"3e50033b-21be-4610-9ded-8efb5b8c03e0\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008502, "request_id": "3e50033b-21be-4610-9ded-8efb5b8c03e0"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.075914", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.009502, \"request_id\": \"9a737403-a2b9-40a1-b7f4-abd4258f9887\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.009502, "request_id": "9a737403-a2b9-40a1-b7f4-abd4258f9887"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.076415", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.009502, \"request_id\": \"cfede2d5-df6c-4a57-8340-2cae3deb13c2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.009502, "request_id": "cfede2d5-df6c-4a57-8340-2cae3deb13c2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.076915", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007502, \"request_id\": \"184b31db-f5ee-4ca4-982f-4d1801232451\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007502, "request_id": "184b31db-f5ee-4ca4-982f-4d1801232451"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.225940", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.226440", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9a012816-1dd5-445e-9cc2-d5ce57f6dced\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9a012816-1dd5-445e-9cc2-d5ce57f6dced"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.227940", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.227940", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"9a012816-1dd5-445e-9cc2-d5ce57f6dced\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "9a012816-1dd5-445e-9cc2-d5ce57f6dced"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.237943", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.238443", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"3be5f611-102e-4188-aea3-89abb7c7e89b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "3be5f611-102e-4188-aea3-89abb7c7e89b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:01.239943", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:01.240444", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"3be5f611-102e-4188-aea3-89abb7c7e89b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "3be5f611-102e-4188-aea3-89abb7c7e89b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.308309", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.308309", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"aac5f132-e666-49ec-a501-4caed98e300b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "aac5f132-e666-49ec-a501-4caed98e300b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.309310", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.309810", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002, \"request_id\": \"aac5f132-e666-49ec-a501-4caed98e300b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002, "request_id": "aac5f132-e666-49ec-a501-4caed98e300b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.344316", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.344816", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.345317", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.345817", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.346816", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"b70efcbb-0018-4401-b184-bbdc886ae3c2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "b70efcbb-0018-4401-b184-bbdc886ae3c2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.346816", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.347317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"14e4bab3-b385-42f4-92b3-236e1d62aabe\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "14e4bab3-b385-42f4-92b3-236e1d62aabe"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.347317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a6e17a09-80af-4f10-b565-c65bf75e4ddf\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a6e17a09-80af-4f10-b565-c65bf75e4ddf"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.347817", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.347817", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"51112834-1dc3-44d2-acc6-472f34fedb4b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "51112834-1dc3-44d2-acc6-472f34fedb4b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.348317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"fbbbbab9-7e6a-4606-85a5-9e7e2f7c85c7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "fbbbbab9-7e6a-4606-85a5-9e7e2f7c85c7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.348817", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.349317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"38f29ba3-a233-4281-99fe-33bb69ce0c41\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "38f29ba3-a233-4281-99fe-33bb69ce0c41"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.349818", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.350317", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.350817", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007001, \"request_id\": \"b70efcbb-0018-4401-b184-bbdc886ae3c2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007001, "request_id": "b70efcbb-0018-4401-b184-bbdc886ae3c2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.351317", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.351317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006501, \"request_id\": \"14e4bab3-b385-42f4-92b3-236e1d62aabe\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006501, "request_id": "14e4bab3-b385-42f4-92b3-236e1d62aabe"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.351818", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.352318", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006002, \"request_id\": \"a6e17a09-80af-4f10-b565-c65bf75e4ddf\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006002, "request_id": "a6e17a09-80af-4f10-b565-c65bf75e4ddf"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.352818", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.352818", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006501, \"request_id\": \"51112834-1dc3-44d2-acc6-472f34fedb4b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006501, "request_id": "51112834-1dc3-44d2-acc6-472f34fedb4b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.353317", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006001, \"request_id\": \"fbbbbab9-7e6a-4606-85a5-9e7e2f7c85c7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006001, "request_id": "fbbbbab9-7e6a-4606-85a5-9e7e2f7c85c7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.353818", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005, \"request_id\": \"38f29ba3-a233-4281-99fe-33bb69ce0c41\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005, "request_id": "38f29ba3-a233-4281-99fe-33bb69ce0c41"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.494843", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.494843", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"de476876-977b-4cd4-8b99-927c486d76fb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "de476876-977b-4cd4-8b99-927c486d76fb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.496343", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.496343", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.001999, \"request_id\": \"de476876-977b-4cd4-8b99-927c486d76fb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.001999, "request_id": "de476876-977b-4cd4-8b99-927c486d76fb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.505344", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.505844", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"082f108a-8d76-4f7e-bbd3-2b6f4f1d1b21\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "082f108a-8d76-4f7e-bbd3-2b6f4f1d1b21"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:03.507345", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:03.507845", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002501, \"request_id\": \"082f108a-8d76-4f7e-bbd3-2b6f4f1d1b21\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002501, "request_id": "082f108a-8d76-4f7e-bbd3-2b6f4f1d1b21"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.574542", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.575542", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"dd136f28-2750-4c81-a6a1-d523fcd75014\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "dd136f28-2750-4c81-a6a1-d523fcd75014"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.577041", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.577541", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002999, \"request_id\": \"dd136f28-2750-4c81-a6a1-d523fcd75014\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002999, "request_id": "dd136f28-2750-4c81-a6a1-d523fcd75014"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.605546", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.606048", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e9035fb8-31e4-4900-9571-452b5a123dab\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e9035fb8-31e4-4900-9571-452b5a123dab"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.609047", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.612548", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.003001, \"request_id\": \"e9035fb8-31e4-4900-9571-452b5a123dab\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.003001, "request_id": "e9035fb8-31e4-4900-9571-452b5a123dab"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.613548", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.614548", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.614548", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.615549", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.616050", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.616548", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f2eaad4c-e674-4dc4-b59c-91812778f316\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f2eaad4c-e674-4dc4-b59c-91812778f316"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.616548", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"0cbcc7ce-d3fc-4ff7-9cb3-08fa4ade75e3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "0cbcc7ce-d3fc-4ff7-9cb3-08fa4ade75e3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.617049", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"2ba4420b-f114-471f-ba2d-4a88a102f9eb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "2ba4420b-f114-471f-ba2d-4a88a102f9eb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.617550", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"2f6a5699-ab0e-47cd-a34e-96b0d9a984e6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "2f6a5699-ab0e-47cd-a34e-96b0d9a984e6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.618050", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e375c430-2abf-431c-a3fe-d3cecc28ec93\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e375c430-2abf-431c-a3fe-d3cecc28ec93"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.619550", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.620049", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.620551", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.621549", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008502, \"request_id\": \"f2eaad4c-e674-4dc4-b59c-91812778f316\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008502, "request_id": "f2eaad4c-e674-4dc4-b59c-91812778f316"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.622550", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008001, \"request_id\": \"0cbcc7ce-d3fc-4ff7-9cb3-08fa4ade75e3\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008001, "request_id": "0cbcc7ce-d3fc-4ff7-9cb3-08fa4ade75e3"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.622550", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007501, \"request_id\": \"2ba4420b-f114-471f-ba2d-4a88a102f9eb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007501, "request_id": "2ba4420b-f114-471f-ba2d-4a88a102f9eb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.623049", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.623049", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.628551", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.009002, \"request_id\": \"2f6a5699-ab0e-47cd-a34e-96b0d9a984e6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.009002, "request_id": "2f6a5699-ab0e-47cd-a34e-96b0d9a984e6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.628551", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007001, \"request_id\": \"e375c430-2abf-431c-a3fe-d3cecc28ec93\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007001, "request_id": "e375c430-2abf-431c-a3fe-d3cecc28ec93"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.782578", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.783078", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"9cb709f4-b10c-4c3c-84e3-29ff25280359\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "9cb709f4-b10c-4c3c-84e3-29ff25280359"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.784578", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.784578", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002001, \"request_id\": \"9cb709f4-b10c-4c3c-84e3-29ff25280359\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002001, "request_id": "9cb709f4-b10c-4c3c-84e3-29ff25280359"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.795080", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.795579", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f77cfba6-fd49-4a39-94c5-bc602c974734\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f77cfba6-fd49-4a39-94c5-bc602c974734"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:41:24.797081", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:41:24.797581", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002501, \"request_id\": \"f77cfba6-fd49-4a39-94c5-bc602c974734\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002501, "request_id": "f77cfba6-fd49-4a39-94c5-bc602c974734"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.585852", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.586852", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"acdf8c20-6179-4710-92e6-a30f14eabc4c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "acdf8c20-6179-4710-92e6-a30f14eabc4c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.600354", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.016s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.600855", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.015502, \"request_id\": \"acdf8c20-6179-4710-92e6-a30f14eabc4c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.015502, "request_id": "acdf8c20-6179-4710-92e6-a30f14eabc4c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.635362", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.635861", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"111c17a0-4ac1-47a3-8181-9aa66667aad0\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "111c17a0-4ac1-47a3-8181-9aa66667aad0"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.639862", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.640863", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.004501, \"request_id\": \"111c17a0-4ac1-47a3-8181-9aa66667aad0\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.004501, "request_id": "111c17a0-4ac1-47a3-8181-9aa66667aad0"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.644863", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.645363", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/error_monitor.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_monitor.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.645864", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/error_viewer.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_viewer.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.647363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"10e9bc51-2bc2-44fc-8f25-e3bccae33611\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "10e9bc51-2bc2-44fc-8f25-e3bccae33611"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.647863", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/error_monitor.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"60dddd43-10ac-434a-b1cc-c461f3d62776\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_monitor.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/error_monitor.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "60dddd43-10ac-434a-b1cc-c461f3d62776"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.648363", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.648864", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/error_viewer.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5ec90798-09bd-4670-9c1b-eb532019f3e7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_viewer.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/error_viewer.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5ec90798-09bd-4670-9c1b-eb532019f3e7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.649363", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.649363", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"f37e6ece-676f-4d36-833e-b0b4984e0a7b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "f37e6ece-676f-4d36-833e-b0b4984e0a7b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.650364", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.650864", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_monitor.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.650864", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"54c61b4e-9839-4f02-a06b-351147b838bd\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "54c61b4e-9839-4f02-a06b-351147b838bd"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.651365", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.007s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_viewer.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.652364", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.656865", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.656865", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007002, \"request_id\": \"10e9bc51-2bc2-44fc-8f25-e3bccae33611\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007002, "request_id": "10e9bc51-2bc2-44fc-8f25-e3bccae33611"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.657365", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006501, \"request_id\": \"60dddd43-10ac-434a-b1cc-c461f3d62776\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_monitor.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006501, "request_id": "60dddd43-10ac-434a-b1cc-c461f3d62776"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.657864", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007002, \"request_id\": \"5ec90798-09bd-4670-9c1b-eb532019f3e7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/error_viewer.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007002, "request_id": "5ec90798-09bd-4670-9c1b-eb532019f3e7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.658365", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0055, \"request_id\": \"f37e6ece-676f-4d36-833e-b0b4984e0a7b\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0055, "request_id": "f37e6ece-676f-4d36-833e-b0b4984e0a7b"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.658865", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"e9f44051-8045-44d2-8dbc-a46f5c5f7cf6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "e9f44051-8045-44d2-8dbc-a46f5c5f7cf6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.658865", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.011s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.661366", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.011002, \"request_id\": \"54c61b4e-9839-4f02-a06b-351147b838bd\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.011002, "request_id": "54c61b4e-9839-4f02-a06b-351147b838bd"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.661865", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.662366", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005001, \"request_id\": \"e9f44051-8045-44d2-8dbc-a46f5c5f7cf6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005001, "request_id": "e9f44051-8045-44d2-8dbc-a46f5c5f7cf6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.666866", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.667367", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"4722081e-ae50-4501-830f-68b03c621bde\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "4722081e-ae50-4501-830f-68b03c621bde"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.669368", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.669867", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"4722081e-ae50-4501-830f-68b03c621bde\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "4722081e-ae50-4501-830f-68b03c621bde"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.808391", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.808891", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7f703391-9987-4728-9aaa-7baccf70209a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7f703391-9987-4728-9aaa-7baccf70209a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.810392", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.810892", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002501, \"request_id\": \"7f703391-9987-4728-9aaa-7baccf70209a\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002501, "request_id": "7f703391-9987-4728-9aaa-7baccf70209a"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.820394", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.820894", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"76cd9909-1a77-47aa-a090-11303f7974f7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "76cd9909-1a77-47aa-a090-11303f7974f7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:08.822893", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:08.823395", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"76cd9909-1a77-47aa-a090-11303f7974f7\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "76cd9909-1a77-47aa-a090-11303f7974f7"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.018689", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.019189", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"4198bc14-d7bd-4615-9521-cd1165f7626d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "4198bc14-d7bd-4615-9521-cd1165f7626d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.031691", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.014s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.031691", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.014003, \"request_id\": \"4198bc14-d7bd-4615-9521-cd1165f7626d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.014003, "request_id": "4198bc14-d7bd-4615-9521-cd1165f7626d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.072697", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.073197", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"48638237-cdf1-4106-a1ca-c8f2c6064458\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "48638237-cdf1-4106-a1ca-c8f2c6064458"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.075197", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.075698", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002501, \"request_id\": \"48638237-cdf1-4106-a1ca-c8f2c6064458\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002501, "request_id": "48638237-cdf1-4106-a1ca-c8f2c6064458"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.089200", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.090200", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"16d826d7-e03f-4ae5-b12f-681f56accd30\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "16d826d7-e03f-4ae5-b12f-681f56accd30"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.092700", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.093200", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.094201", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.094701", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"476952fc-2904-42e9-b6e4-ec41b23047ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "476952fc-2904-42e9-b6e4-ec41b23047ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.094701", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.005s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.095200", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.095200", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"949e6494-a935-4c88-93d5-363ed8076690\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "949e6494-a935-4c88-93d5-363ed8076690"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.095701", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"160a3cf0-440f-41fd-9f22-1841d21091ef\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "160a3cf0-440f-41fd-9f22-1841d21091ef"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.096201", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.004501, \"request_id\": \"16d826d7-e03f-4ae5-b12f-681f56accd30\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.004501, "request_id": "16d826d7-e03f-4ae5-b12f-681f56accd30"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.096701", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"7ffd3f0c-f0d4-4f68-8e38-133d3c212be9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "7ffd3f0c-f0d4-4f68-8e38-133d3c212be9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.097201", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.098202", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.098702", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.098702", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005501, \"request_id\": \"476952fc-2904-42e9-b6e4-ec41b23047ee\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005501, "request_id": "476952fc-2904-42e9-b6e4-ec41b23047ee"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.099201", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006002, \"request_id\": \"949e6494-a935-4c88-93d5-363ed8076690\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006002, "request_id": "949e6494-a935-4c88-93d5-363ed8076690"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.099702", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.005502, \"request_id\": \"160a3cf0-440f-41fd-9f22-1841d21091ef\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.005502, "request_id": "160a3cf0-440f-41fd-9f22-1841d21091ef"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.100202", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.006s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.103203", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.006001, \"request_id\": \"7ffd3f0c-f0d4-4f68-8e38-133d3c212be9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.006001, "request_id": "7ffd3f0c-f0d4-4f68-8e38-133d3c212be9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.267732", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.268732", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a969ce6e-c0bb-4ec3-96be-d98568af56f5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a969ce6e-c0bb-4ec3-96be-d98568af56f5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.270232", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.270732", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0025, \"request_id\": \"a969ce6e-c0bb-4ec3-96be-d98568af56f5\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0025, "request_id": "a969ce6e-c0bb-4ec3-96be-d98568af56f5"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.280234", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.280734", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"a00ea5c6-9119-4db4-a626-5aaef6ab7f37\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "a00ea5c6-9119-4db4-a626-5aaef6ab7f37"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:19.282233", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:19.282734", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.001999, \"request_id\": \"a00ea5c6-9119-4db4-a626-5aaef6ab7f37\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.001999, "request_id": "a00ea5c6-9119-4db4-a626-5aaef6ab7f37"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.365424", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.365424", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"6b85d48c-d60c-427d-a35c-cf67175af0e2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "6b85d48c-d60c-427d-a35c-cf67175af0e2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.366424", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.366924", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0015, \"request_id\": \"6b85d48c-d60c-427d-a35c-cf67175af0e2\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0015, "request_id": "6b85d48c-d60c-427d-a35c-cf67175af0e2"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.404431", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/d8_form.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.404930", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/d8_form.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"3d47bd88-fa7f-4702-965f-e523915c8fd9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/d8_form.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "3d47bd88-fa7f-4702-965f-e523915c8fd9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.407431", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.003s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.407932", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.003002, \"request_id\": \"3d47bd88-fa7f-4702-965f-e523915c8fd9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/d8_form.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.003002, "request_id": "3d47bd88-fa7f-4702-965f-e523915c8fd9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.411432", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/css/conversation_styles.css", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.412432", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_ui.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.413432", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/d8_form.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.413933", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/js/conversation_manager.js", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.414433", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/css/conversation_styles.css\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"2dc36505-6e9b-46c6-8890-eef8ee6a0a9c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/css/conversation_styles.css", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "2dc36505-6e9b-46c6-8890-eef8ee6a0a9c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.414932", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.415433", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_ui.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"815fed1e-f03e-4424-a661-4c10413db996\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_ui.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "815fed1e-f03e-4424-a661-4c10413db996"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.415433", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/d8_form.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"8e7457e1-9c86-4d25-972d-6a850c34570c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/d8_form.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "8e7457e1-9c86-4d25-972d-6a850c34570c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.415933", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/js/conversation_manager.js\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"45863981-6a36-4e6d-b11a-b452a3f9e491\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/js/conversation_manager.js", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "45863981-6a36-4e6d-b11a-b452a3f9e491"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.416434", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"5046ad31-17aa-498d-b585-fc42335338bb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "5046ad31-17aa-498d-b585-fc42335338bb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.416932", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.418434", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.419933", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.420433", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.007501, \"request_id\": \"2dc36505-6e9b-46c6-8890-eef8ee6a0a9c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/css/conversation_styles.css"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.007501, "request_id": "2dc36505-6e9b-46c6-8890-eef8ee6a0a9c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.420934", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008002, \"request_id\": \"815fed1e-f03e-4424-a661-4c10413db996\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_ui.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008002, "request_id": "815fed1e-f03e-4424-a661-4c10413db996"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.421434", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008002, \"request_id\": \"8e7457e1-9c86-4d25-972d-6a850c34570c\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/d8_form.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008002, "request_id": "8e7457e1-9c86-4d25-972d-6a850c34570c"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.421434", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.009s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.421934", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.008s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.426435", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.009001, \"request_id\": \"45863981-6a36-4e6d-b11a-b452a3f9e491\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/js/conversation_manager.js"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.009001, "request_id": "45863981-6a36-4e6d-b11a-b452a3f9e491"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.426934", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.008002, \"request_id\": \"5046ad31-17aa-498d-b585-fc42335338bb\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.008002, "request_id": "5046ad31-17aa-498d-b585-fc42335338bb"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.591464", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.591964", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"99d89507-4e9c-4fe4-98d2-00b849995932\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "99d89507-4e9c-4fe4-98d2-00b849995932"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.593464", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.002s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.593464", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.002001, \"request_id\": \"99d89507-4e9c-4fe4-98d2-00b849995932\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.002001, "request_id": "99d89507-4e9c-4fe4-98d2-00b849995932"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.605467", "level": "INFO", "logger": "app", "message": "收到请求: GET http://************:5555/static/favicon.png", "module": "app", "function": "log_request_info", "line": 36, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.605966", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"GET\", \"url\": \"http://************:5555/static/favicon.png\", \"ip\": \"************\", \"user_agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\", \"request_id\": \"58e00aca-114d-48ad-9f50-5a23c043d133\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_request", "details": {"method": "GET", "url": "http://************:5555/static/favicon.png", "ip": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "request_id": "58e00aca-114d-48ad-9f50-5a23c043d133"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T12:46:20.608967", "level": "INFO", "logger": "app", "message": "响应完成: 200 (0.004s)", "module": "app", "function": "log_response_info", "line": 51, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}}
{"timestamp": "2025-07-31T12:46:20.609467", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 0.0035, \"request_id\": \"58e00aca-114d-48ad-9f50-5a23c043d133\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "method": "GET", "url": "http://************:5555/static/favicon.png"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 0.0035, "request_id": "58e00aca-114d-48ad-9f50-5a23c043d133"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:27:48.467320", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://************:5555/submit_8d_report\", \"ip\": \"************\", \"request_id\": \"904771ae-7329-47c6-9aab-e40ab9d07ae6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "method": "POST", "url": "http://************:5555/submit_8d_report"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://************:5555/submit_8d_report", "ip": "************", "request_id": "904771ae-7329-47c6-9aab-e40ab9d07ae6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:27:48.467820", "level": "INFO", "logger": "business", "message": "BUSINESS_OP: {\"operation\": \"submit_8d_report\", \"details\": {\"form_data_keys\": [\"D0汇报信息\", \"D1建立小组\", \"D2问题描述\", \"D3临时措施\", \"D4根本原因\", \"D5永久措施\", \"D6措施验证\", \"D7预防措施\", \"D8庆贺团队\"], \"has_ai_requirements\": false}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "method": "POST", "url": "http://************:5555/submit_8d_report"}, "session_info": {}, "operation": "submit_8d_report", "details": {"form_data_keys": ["D0汇报信息", "D1建立小组", "D2问题描述", "D3临时措施", "D4根本原因", "D5永久措施", "D6措施验证", "D7预防措施", "D8庆贺团队"], "has_ai_requirements": false}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:28:42.337584", "level": "INFO", "logger": "business", "message": "BUSINESS_OP: {\"operation\": \"ai_enhancement_success\", \"details\": {\"session_id\": \"7c547378-480b-4fea-82c8-d97f1d35d88d\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "method": "POST", "url": "http://************:5555/submit_8d_report"}, "session_info": {}, "operation": "ai_enhancement_success", "details": {"session_id": "7c547378-480b-4fea-82c8-d97f1d35d88d"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:28:42.338085", "level": "INFO", "logger": "business", "message": "BUSINESS_OP: {\"operation\": \"save_session_report\", \"details\": {\"session_id\": \"7c547378-480b-4fea-82c8-d97f1d35d88d\", \"data_keys\": [\"D0汇报信息\", \"D1建立小组\", \"D2问题描述\", \"D3临时措施\", \"D4根本原因\", \"D5永久措施\", \"D6措施验证\", \"D7预防措施\", \"D8庆贺团队\"]}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "method": "POST", "url": "http://************:5555/submit_8d_report"}, "session_info": {}, "operation": "save_session_report", "details": {"session_id": "7c547378-480b-4fea-82c8-d97f1d35d88d", "data_keys": ["D0汇报信息", "D1建立小组", "D2问题描述", "D3临时措施", "D4根本原因", "D5永久措施", "D6措施验证", "D7预防措施", "D8庆贺团队"]}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:28:42.338584", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 200, \"duration\": 53.871265, \"request_id\": \"904771ae-7329-47c6-9aab-e40ab9d07ae6\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "************", "method": "POST", "url": "http://************:5555/submit_8d_report"}, "session_info": {}, "action": "http_response", "details": {"status_code": 200, "duration": 53.871265, "request_id": "904771ae-7329-47c6-9aab-e40ab9d07ae6"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:47:56.162263", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_docx\", \"ip\": \"127.0.0.1\", \"request_id\": \"a49199ef-b208-47a2-a5c5-3c58070304ff\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_docx"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_docx", "ip": "127.0.0.1", "request_id": "a49199ef-b208-47a2-a5c5-3c58070304ff"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T13:47:56.163263", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 404, \"duration\": 0.001, \"request_id\": \"a49199ef-b208-47a2-a5c5-3c58070304ff\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_docx"}, "session_info": {}, "action": "http_response", "details": {"status_code": 404, "duration": 0.001, "request_id": "a49199ef-b208-47a2-a5c5-3c58070304ff"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:02:25.132039", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_docx\", \"ip\": \"127.0.0.1\", \"request_id\": \"fdf2d15f-ddc3-4197-bad5-897e22c05e08\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_docx"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_docx", "ip": "127.0.0.1", "request_id": "fdf2d15f-ddc3-4197-bad5-897e22c05e08"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:06:45.941865", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_ppt\", \"ip\": \"127.0.0.1\", \"request_id\": \"54d84bd0-0f41-455f-b27d-4e2fe20af420\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_ppt"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_ppt", "ip": "127.0.0.1", "request_id": "54d84bd0-0f41-455f-b27d-4e2fe20af420"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:06:45.942865", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_response\", \"details\": {\"status_code\": 404, \"duration\": 0.0015, \"request_id\": \"54d84bd0-0f41-455f-b27d-4e2fe20af420\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_ppt"}, "session_info": {}, "action": "http_response", "details": {"status_code": 404, "duration": 0.0015, "request_id": "54d84bd0-0f41-455f-b27d-4e2fe20af420"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:15:42.875503", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_docx\", \"ip\": \"127.0.0.1\", \"request_id\": \"e19e311e-64f5-4490-917a-84aba835c732\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_docx"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_docx", "ip": "127.0.0.1", "request_id": "e19e311e-64f5-4490-917a-84aba835c732"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:17:23.278662", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_ppt\", \"ip\": \"127.0.0.1\", \"request_id\": \"54022fa2-e1d6-4187-b3fd-d14db8f0efff\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_ppt"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_ppt", "ip": "127.0.0.1", "request_id": "54022fa2-e1d6-4187-b3fd-d14db8f0efff"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:17:40.448175", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_ppt\", \"ip\": \"127.0.0.1\", \"request_id\": \"fc665f11-6f6e-4e28-835b-2aeb13b3b8c9\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_ppt"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_ppt", "ip": "127.0.0.1", "request_id": "fc665f11-6f6e-4e28-835b-2aeb13b3b8c9"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:19:38.712938", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_ppt\", \"ip\": \"127.0.0.1\", \"request_id\": \"53d22b19-ac33-4f77-9579-dd40cd144423\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_ppt"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_ppt", "ip": "127.0.0.1", "request_id": "53d22b19-ac33-4f77-9579-dd40cd144423"}, "user_id": null, "session_id": null}
{"timestamp": "2025-07-31T14:19:57.507736", "level": "INFO", "logger": "business", "message": "USER_ACTION: {\"action\": \"http_request\", \"details\": {\"method\": \"POST\", \"url\": \"http://localhost:5555/process_docx\", \"ip\": \"127.0.0.1\", \"request_id\": \"54abbea8-b964-40ef-bd20-55e14bb75b6e\"}, \"user_id\": null, \"session_id\": null}", "module": "", "function": null, "line": 0, "request_info": {"ip_address": "127.0.0.1", "method": "POST", "url": "http://localhost:5555/process_docx"}, "session_info": {}, "action": "http_request", "details": {"method": "POST", "url": "http://localhost:5555/process_docx", "ip": "127.0.0.1", "request_id": "54abbea8-b964-40ef-bd20-55e14bb75b6e"}, "user_id": null, "session_id": null}
